# DrissionPage 代码库地图 (Code Base Map)

> **版本**: 4.1.0.13  
> **作者**: g1879  
> **生成时间**: 2025-01-30  
> **数据来源**: Context7, DeepWiki, MCP-Git-Ingest

## 📋 目录

1. [项目概述](#项目概述)
2. [核心架构](#核心架构)
3. [目录结构详解](#目录结构详解)
4. [核心类与模块](#核心类与模块)
5. [API 接口总览](#api-接口总览)
6. [使用模式与工作流](#使用模式与工作流)
7. [扩展功能](#扩展功能)
8. [配置系统](#配置系统)
9. [错误处理](#错误处理)
10. [最佳实践](#最佳实践)

---

## 📖 项目概述

DrissionPage 是一个基于 Python 的网页自动化工具，它既能控制浏览器，也能收发数据包，还能把两者合而为一。可兼顾浏览器自动化的便利性和 requests 的高效率。

### 🎯 核心特性

- **双模式操作**: 浏览器控制模式 + HTTP请求模式
- **自研内核**: 不基于 WebDriver，运行速度更快
- **跨iframe查找**: 无需切入切出，逻辑更清晰
- **多标签页操作**: 可同时操作多个标签页
- **强大的元素定位**: 极简的定位语法
- **内置等待重试**: 自动处理网络不稳定问题
- **集成下载工具**: 快捷可靠的下载功能

### 🔧 技术栈

- **核心依赖**: lxml, requests, cssselect, websocket-client
- **支持系统**: Windows, Linux, Mac
- **Python版本**: 3.6+
- **浏览器支持**: Chromium内核浏览器 (Chrome, Edge)

---

## 🏗️ 核心架构

```mermaid
graph TB
    subgraph "DrissionPage 架构"
        User[用户代码] --> API[统一API接口]
        
        API --> WebPage[WebPage<br/>混合模式]
        API --> ChromiumPage[ChromiumPage<br/>浏览器模式]
        API --> SessionPage[SessionPage<br/>HTTP模式]
        
        WebPage --> Browser[浏览器控制]
        WebPage --> HTTP[HTTP请求]
        ChromiumPage --> Browser
        SessionPage --> HTTP
        
        Browser --> CDP[Chrome DevTools Protocol]
        HTTP --> Requests[Requests库]
        
        CDP --> Chrome[Chrome/Edge浏览器]
        Requests --> Server[Web服务器]
    end
```

### 🔄 三种操作模式

1. **ChromiumPage (浏览器模式)**
   - 完整的浏览器自动化功能
   - JavaScript执行支持
   - 实时交互能力
   - 适用于复杂的用户交互场景

2. **SessionPage (HTTP模式)**
   - 轻量级HTTP请求处理
   - 高效的数据抓取
   - 低资源消耗
   - 适用于纯数据采集场景

3. **WebPage (混合模式)**
   - 结合两种模式的优势
   - 可在运行时切换模式
   - 灵活的工作流程
   - 适用于复杂的自动化任务

---

## 📁 目录结构详解

```
DrissionPage/
├── __init__.py                 # 主入口，导出核心类
├── common.py                   # 公共工具和集成函数
├── errors.py                   # 异常定义
├── items.py                    # 数据项定义
│
├── _base/                      # 基础组件
│   ├── base.py                 # 基础抽象类
│   ├── chromium.py             # Chromium浏览器管理器
│   └── driver.py               # CDP驱动程序
│
├── _configs/                   # 配置管理
│   ├── chromium_options.py     # 浏览器配置选项
│   ├── session_options.py      # HTTP会话配置选项
│   ├── options_manage.py       # 配置管理器
│   └── configs.ini             # 默认配置文件
│
├── _elements/                  # 元素系统
│   ├── chromium_element.py     # 浏览器元素类
│   ├── session_element.py      # HTTP元素类
│   └── none_element.py         # 空元素类
│
├── _functions/                 # 功能函数
│   ├── browser.py              # 浏览器相关函数
│   ├── elements.py             # 元素查找函数
│   ├── locator.py              # 定位器解析
│   ├── keys.py                 # 键盘按键定义
│   ├── tools.py                # 工具函数
│   ├── web.py                  # Web相关函数
│   ├── cookies.py              # Cookie处理
│   ├── by.py                   # 定位方式定义
│   ├── settings.py             # 全局设置
│   └── cli.py                  # 命令行接口
│
├── _pages/                     # 页面对象
│   ├── chromium_page.py        # 浏览器页面类
│   ├── chromium_tab.py         # 浏览器标签页类
│   ├── chromium_frame.py       # 浏览器框架类
│   ├── chromium_base.py        # 浏览器基础类
│   ├── session_page.py         # HTTP页面类
│   ├── web_page.py             # 混合页面类
│   └── mix_tab.py              # 混合标签页类
│
└── _units/                     # 功能单元
    ├── actions.py              # 动作执行器
    ├── clicker.py              # 点击器
    ├── waiter.py               # 等待器
    ├── scroller.py             # 滚动器
    ├── setter.py               # 设置器
    ├── states.py               # 状态检查器
    ├── selector.py             # 选择器
    ├── listener.py             # 网络监听器
    ├── downloader.py           # 下载器
    ├── screencast.py           # 屏幕录制
    ├── console.py              # 控制台
    ├── cookies_setter.py       # Cookie设置器
    └── rect.py                 # 矩形位置
```

---

## 🔧 核心类与模块

### 📄 页面对象层次结构

```mermaid
classDiagram
    class BasePage {
        <<Abstract>>
        +ele()
        +eles()
        +get()
    }
    
    class ChromiumBase {
        +run_cdp()
        +run_js()
        +cookies()
        +handle_alert()
        +wait
        +set
        +actions
    }
    
    class SessionPage {
        +get()
        +post()
        +download()
        +json
        +response
    }
    
    class ChromiumPage {
        +new_tab()
        +get_tab()
        +close_tabs()
        +quit()
    }
    
    class WebPage {
        +change_mode()
        +to_session()
        +to_chromium()
    }
    
    BasePage <|-- ChromiumBase
    BasePage <|-- SessionPage
    ChromiumBase <|-- ChromiumPage
    ChromiumBase <|-- WebPage
    SessionPage <|-- WebPage
```

### 🧩 元素系统

```mermaid
classDiagram
    class DrissionElement {
        <<Abstract>>
        +tag
        +text
        +html
        +attrs
        +parent()
        +next()
        +prev()
    }
    
    class ChromiumElement {
        +click()
        +input()
        +hover()
        +scroll
        +wait
        +states
        +rect
    }
    
    class SessionElement {
        +link
        +src
        +value
        +selected
    }
    
    class NoneElement {
        +__bool__() False
    }
    
    DrissionElement <|-- ChromiumElement
    DrissionElement <|-- SessionElement
    DrissionElement <|-- NoneElement
```

---

## 🚀 API 接口总览

### 🌐 页面操作 API

#### ChromiumPage 核心方法
```python
# 页面导航
page.get(url)                    # 访问URL
page.back()                      # 后退
page.forward()                   # 前进
page.refresh()                   # 刷新

# 标签页管理
page.new_tab(url)               # 新建标签页
page.get_tab(index)             # 获取标签页
page.close_tabs(tabs)           # 关闭标签页

# JavaScript执行
page.run_js(script)             # 执行JS代码
page.run_cdp(cmd, **kwargs)     # 执行CDP命令

# 等待操作
page.wait.load_start()          # 等待页面开始加载
page.wait.doc_loaded()          # 等待文档加载完成
page.wait.ele_loaded(locator)   # 等待元素加载
```

#### SessionPage 核心方法
```python
# HTTP请求
page.get(url, **kwargs)         # GET请求
page.post(url, data, **kwargs)  # POST请求
page.download(url, path)        # 下载文件

# 响应处理
page.html                       # 页面HTML
page.json                       # JSON响应
page.response                   # Response对象
page.cookies                    # Cookie信息
```

### 🎯 元素定位 API

#### 定位语法
```python
# 基础定位
page.ele('#id')                 # ID定位
page.ele('.class')              # 类名定位
page.ele('tag:div')             # 标签定位
page.ele('@attr=value')         # 属性定位
page.ele('text:内容')           # 文本定位

# 复合定位
page.ele('tag:div@class=item')  # 标签+属性
page.ele('@@attr1=val1@attr2=val2')  # 多属性
page.ele('tag:div@|class=a@class=b')  # 或条件

# XPath和CSS
page.ele('xpath://div[@id="test"]')  # XPath
page.ele('css:div.item')             # CSS选择器
```

#### 元素操作 API
```python
# 基础交互
element.click()                 # 点击
element.input('text')           # 输入文本
element.clear()                 # 清空内容
element.hover()                 # 悬停

# 高级交互
element.click.right()           # 右键点击
element.click.at(x, y)          # 指定位置点击
element.drag_to(target)         # 拖拽到目标

# 属性获取
element.text                    # 文本内容
element.html                    # HTML内容
element.attr('name')            # 属性值
element.attrs                   # 所有属性

# 状态检查
element.states.is_displayed     # 是否显示
element.states.is_enabled       # 是否启用
element.states.is_selected      # 是否选中
```

---

## 🔄 使用模式与工作流

### 🎨 典型使用场景

#### 1. 纯浏览器自动化
```python
from DrissionPage import ChromiumPage

page = ChromiumPage()
page.get('https://example.com')
page.ele('#username').input('user123')
page.ele('#password').input('password123')
page.ele('#login-btn').click()
```

#### 2. 纯HTTP数据抓取
```python
from DrissionPage import SessionPage

page = SessionPage()
page.get('https://api.example.com/data')
data = page.json
for item in data['items']:
    print(item['name'])
```

#### 3. 混合模式工作流
```python
from DrissionPage import WebPage

page = WebPage()
# 浏览器模式登录
page.get('https://example.com/login')
page.ele('#username').input('user123')
page.ele('#password').input('password123')
page.ele('#login-btn').click()

# 切换到HTTP模式进行数据抓取
page.change_mode('s')
page.get('https://example.com/api/data')
data = page.json
```

### 🔧 高级功能使用

#### 网络监听
```python
# 开始监听网络请求
page.listen.start('api/data')
page.ele('#load-data-btn').click()
packet = page.listen.wait()
print(packet.response.body)
```

#### 文件下载
```python
# 设置下载路径
page.set.download_path('./downloads')
page.ele('#download-link').click()
page.wait.download_begin()
```

#### 多标签页操作
```python
# 创建新标签页
tab1 = page.new_tab('https://site1.com')
tab2 = page.new_tab('https://site2.com')

# 并行操作
tab1.ele('#button1').click()
tab2.ele('#button2').click()
```

---

## ⚙️ 配置系统

### 🔧 ChromiumOptions 配置

```python
from DrissionPage import ChromiumOptions

co = ChromiumOptions()
# 基础配置
co.set_browser_path('/path/to/chrome')
co.set_user_data_path('/path/to/userdata')
co.set_download_path('/path/to/downloads')

# 浏览器参数
co.headless()                   # 无头模式
co.incognito()                  # 隐身模式
co.no_imgs()                    # 不加载图片
co.mute()                       # 静音
co.set_window_size(1920, 1080)  # 窗口大小

# 高级配置
co.set_argument('--disable-gpu')
co.set_pref('profile.default_content_settings.popups', 0)
```

### 🌐 SessionOptions 配置

```python
from DrissionPage import SessionOptions

so = SessionOptions()
# 请求配置
so.set_headers({'User-Agent': 'Custom Agent'})
so.set_proxies(http='http://proxy:8080')
so.set_timeout(30)
so.set_verify(False)

# Cookie配置
so.set_cookies(['name=value; domain=.example.com'])
```

---

## 🚨 错误处理

### 📋 异常层次结构

```python
BaseError                      # 基础异常
├── ElementNotFoundError       # 元素未找到
├── AlertExistsError          # 存在未处理的提示框
├── ContextLostError          # 页面上下文丢失
├── ElementLostError          # 元素对象失效
├── CDPError                  # CDP方法调用错误
├── PageDisconnectedError     # 页面连接断开
├── JavaScriptError           # JavaScript运行错误
├── NoRectError               # 元素无位置信息
├── BrowserConnectError       # 浏览器连接失败
├── NoResourceError           # 无可保存内容
├── CanNotClickError          # 元素无法点击
├── GetDocumentError          # 获取文档失败
├── WaitTimeoutError          # 等待超时
├── WrongURLError             # 无效URL
├── StorageError              # 存储操作错误
├── CookieFormatError         # Cookie格式错误
└── TargetNotFoundError       # 找不到指定页面
```

### 🛡️ 错误处理最佳实践

```python
from DrissionPage import ChromiumPage
from DrissionPage.errors import ElementNotFoundError, WaitTimeoutError

try:
    page = ChromiumPage()
    page.get('https://example.com')
    element = page.ele('#target-element', timeout=5)
    element.click()
except ElementNotFoundError:
    print("元素未找到，请检查定位器")
except WaitTimeoutError:
    print("等待超时，页面可能加载缓慢")
except Exception as e:
    print(f"其他错误: {e}")
```

---

## 💡 最佳实践

### 🎯 性能优化

1. **合理选择模式**
   - 纯数据抓取使用 SessionPage
   - 需要JavaScript交互使用 ChromiumPage
   - 复杂流程使用 WebPage 混合模式

2. **元素定位优化**
   - 优先使用ID和唯一属性
   - 避免过于复杂的XPath
   - 使用DrissionPage简化语法

3. **等待策略**
   - 设置合理的超时时间
   - 使用特定的等待条件
   - 避免固定时间等待

### 🔒 稳定性保障

1. **异常处理**
   - 捕获特定异常类型
   - 实现重试机制
   - 记录详细错误信息

2. **资源管理**
   - 及时关闭页面和浏览器
   - 清理临时文件
   - 监控内存使用

3. **网络处理**
   - 配置合理的超时时间
   - 处理网络中断
   - 使用代理池

### 📝 代码组织

1. **模块化设计**
   - 分离页面对象和业务逻辑
   - 使用配置文件管理设置
   - 创建可复用的工具函数

2. **测试策略**
   - 编写单元测试
   - 模拟网络环境
   - 验证关键功能

---

## 📚 参考资源

- **官方文档**: [https://DrissionPage.cn](https://drissionpage.cn)
- **GitHub仓库**: [https://github.com/g1879/DrissionPage](https://github.com/g1879/DrissionPage)
- **Gitee仓库**: [https://gitee.com/g1879/DrissionPage](https://gitee.com/g1879/DrissionPage)

---

## 🔍 详细模块分析

### 📦 核心依赖关系

```mermaid
graph LR
    subgraph "外部依赖"
        lxml[lxml<br/>XML/HTML解析]
        requests[requests<br/>HTTP客户端]
        cssselect[cssselect<br/>CSS选择器]
        websocket[websocket-client<br/>WebSocket通信]
        downloadkit[DownloadKit<br/>下载工具]
        click[click<br/>命令行接口]
        tldextract[tldextract<br/>域名提取]
        psutil[psutil<br/>系统进程]
    end

    subgraph "DrissionPage核心"
        Core[DrissionPage核心]
    end

    lxml --> Core
    requests --> Core
    cssselect --> Core
    websocket --> Core
    downloadkit --> Core
    click --> Core
    tldextract --> Core
    psutil --> Core
```

### 🧩 模块间依赖关系

```mermaid
graph TD
    subgraph "配置层"
        CO[ChromiumOptions]
        SO[SessionOptions]
        OM[OptionsManager]
    end

    subgraph "基础层"
        Base[BasePage]
        Chromium[Chromium]
        Driver[Driver]
    end

    subgraph "页面层"
        CP[ChromiumPage]
        SP[SessionPage]
        WP[WebPage]
        CT[ChromiumTab]
        CF[ChromiumFrame]
    end

    subgraph "元素层"
        CE[ChromiumElement]
        SE[SessionElement]
        NE[NoneElement]
    end

    subgraph "功能层"
        Actions[Actions]
        Waiter[Waiter]
        Listener[Listener]
        Downloader[Downloader]
    end

    CO --> CP
    SO --> SP
    CO --> WP
    SO --> WP

    Base --> CP
    Base --> SP
    Chromium --> CP
    Driver --> Chromium

    CP --> CE
    SP --> SE
    CP --> CT
    CP --> CF

    CE --> Actions
    CE --> Waiter
    CP --> Listener
    SP --> Downloader
```

### 📋 关键文件功能说明

#### 🏗️ 基础架构文件

| 文件路径 | 主要功能 | 关键类/函数 |
|---------|---------|------------|
| `_base/base.py` | 定义所有页面类的基础抽象类 | `BasePage`, `DrissionElement` |
| `_base/chromium.py` | Chromium浏览器管理器，处理浏览器生命周期 | `Chromium` |
| `_base/driver.py` | CDP协议驱动，与浏览器通信 | `Driver` |

#### ⚙️ 配置管理文件

| 文件路径 | 主要功能 | 关键类/函数 |
|---------|---------|------------|
| `_configs/chromium_options.py` | 浏览器启动和运行配置 | `ChromiumOptions` |
| `_configs/session_options.py` | HTTP会话配置 | `SessionOptions` |
| `_configs/options_manage.py` | 配置文件读写管理 | `OptionsManager` |
| `_configs/configs.ini` | 默认配置参数 | 配置文件 |

#### 🎯 元素系统文件

| 文件路径 | 主要功能 | 关键类/函数 |
|---------|---------|------------|
| `_elements/chromium_element.py` | 浏览器模式下的元素操作 | `ChromiumElement` |
| `_elements/session_element.py` | HTTP模式下的元素解析 | `SessionElement` |
| `_elements/none_element.py` | 空元素处理，避免异常 | `NoneElement` |

#### 🔧 功能函数文件

| 文件路径 | 主要功能 | 关键类/函数 |
|---------|---------|------------|
| `_functions/elements.py` | 元素查找和集合操作 | `find_elements`, `ElementsList` |
| `_functions/locator.py` | 定位器解析和转换 | `get_loc`, `translate_loc` |
| `_functions/browser.py` | 浏览器相关工具函数 | `connect_browser`, `get_browser_port` |
| `_functions/tools.py` | 通用工具函数 | `wait_until`, `clean_folder` |
| `_functions/keys.py` | 键盘按键常量定义 | `Keys` |
| `_functions/by.py` | 定位方式常量定义 | `By` |
| `_functions/settings.py` | 全局设置管理 | `Settings` |
| `_functions/cli.py` | 命令行接口实现 | `main`, CLI命令 |

#### 📄 页面对象文件

| 文件路径 | 主要功能 | 关键类/函数 |
|---------|---------|------------|
| `_pages/chromium_page.py` | 浏览器页面主类 | `ChromiumPage` |
| `_pages/session_page.py` | HTTP页面主类 | `SessionPage` |
| `_pages/web_page.py` | 混合模式页面类 | `WebPage` |
| `_pages/chromium_tab.py` | 浏览器标签页管理 | `ChromiumTab` |
| `_pages/chromium_frame.py` | iframe框架处理 | `ChromiumFrame` |
| `_pages/chromium_base.py` | 浏览器页面基础类 | `ChromiumBase` |

#### 🛠️ 功能单元文件

| 文件路径 | 主要功能 | 关键类/函数 |
|---------|---------|------------|
| `_units/actions.py` | 鼠标键盘动作执行 | `Actions` |
| `_units/clicker.py` | 点击操作处理 | `Clicker` |
| `_units/waiter.py` | 等待条件管理 | `Waiter` |
| `_units/scroller.py` | 滚动操作控制 | `Scroller` |
| `_units/setter.py` | 属性设置管理 | `Setter` |
| `_units/states.py` | 元素状态检查 | `States` |
| `_units/listener.py` | 网络请求监听 | `Listener` |
| `_units/downloader.py` | 文件下载管理 | `Downloader` |
| `_units/screencast.py` | 屏幕录制功能 | `Screencast` |
| `_units/console.py` | 浏览器控制台 | `Console` |

---

## 🎨 设计模式与架构原则

### 🏛️ 设计模式应用

1. **工厂模式 (Factory Pattern)**
   - `ChromiumOptions` 和 `SessionOptions` 作为配置工厂
   - 根据不同需求创建相应的页面对象

2. **策略模式 (Strategy Pattern)**
   - 不同的元素定位策略 (ID, CSS, XPath, 文本等)
   - 不同的等待策略 (元素出现, 页面加载, 网络请求等)

3. **装饰器模式 (Decorator Pattern)**
   - 元素操作的增强功能 (等待, 重试, 状态检查)
   - 页面操作的自动化包装

4. **观察者模式 (Observer Pattern)**
   - 网络请求监听器
   - 页面状态变化监听

5. **代理模式 (Proxy Pattern)**
   - `NoneElement` 作为空元素的代理
   - 避免空指针异常

### 🎯 架构原则

1. **单一职责原则 (SRP)**
   - 每个类专注于特定功能
   - 清晰的模块划分

2. **开闭原则 (OCP)**
   - 通过继承扩展功能
   - 配置化的行为定制

3. **里氏替换原则 (LSP)**
   - 元素类的多态性
   - 页面类的统一接口

4. **接口隔离原则 (ISP)**
   - 功能单元的独立性
   - 最小化依赖关系

5. **依赖倒置原则 (DIP)**
   - 基于抽象的设计
   - 配置驱动的实现

---

## 🚀 性能优化策略

### ⚡ 速度优化

1. **模式选择优化**
   ```python
   # 纯数据抓取 - 使用SessionPage (快)
   page = SessionPage()

   # 需要JS交互 - 使用ChromiumPage (功能完整)
   page = ChromiumPage()

   # 复杂流程 - 使用WebPage混合模式 (灵活)
   page = WebPage()
   ```

2. **元素定位优化**
   ```python
   # 优先级: ID > 类名 > 属性 > XPath > 文本
   page.ele('#fast-id')           # 最快
   page.ele('.class-name')        # 较快
   page.ele('@data-id=123')       # 中等
   page.ele('xpath://div[@id]')   # 较慢
   page.ele('text:显示文本')       # 最慢
   ```

3. **批量操作优化**
   ```python
   # 使用静态元素集合进行批量处理
   elements = page.s_eles('.item')  # 静态元素，性能更好
   for ele in elements:
       print(ele.text)
   ```

### 💾 内存优化

1. **资源管理**
   ```python
   # 及时关闭页面和浏览器
   page.close()
   page.quit()

   # 清理下载文件
   from DrissionPage._functions.tools import clean_folder
   clean_folder('./downloads', ignore=['important.pdf'])
   ```

2. **配置优化**
   ```python
   co = ChromiumOptions()
   co.no_imgs()        # 不加载图片
   co.mute()           # 静音
   co.headless()       # 无头模式
   ```

---

## 🔐 安全性考虑

### 🛡️ 安全最佳实践

1. **数据保护**
   ```python
   # 使用环境变量存储敏感信息
   import os
   username = os.getenv('LOGIN_USERNAME')
   password = os.getenv('LOGIN_PASSWORD')
   ```

2. **网络安全**
   ```python
   # 配置代理和SSL验证
   so = SessionOptions()
   so.set_proxies(http='http://proxy:8080')
   so.set_verify(True)  # 启用SSL验证
   ```

3. **浏览器安全**
   ```python
   co = ChromiumOptions()
   co.incognito()      # 隐身模式
   co.set_argument('--no-sandbox')  # 根据需要配置沙箱
   ```

### ⚠️ 使用条款遵守

根据项目许可证，使用时需要注意：
- 仅限个人学习和合法非盈利目的
- 遵守Robots协议
- 不得用于攻击和骚扰行为
- 不得损害他人利益

---

## 📈 监控与调试

### 🔍 调试技巧

1. **网络监听调试**
   ```python
   # 监听特定API请求
   page.listen.start('api/data')
   page.ele('#trigger-btn').click()
   packet = page.listen.wait()
   print(f"请求URL: {packet.url}")
   print(f"响应状态: {packet.response.status}")
   print(f"响应内容: {packet.response.body}")
   ```

2. **元素状态调试**
   ```python
   element = page.ele('#target')
   print(f"是否显示: {element.states.is_displayed}")
   print(f"是否启用: {element.states.is_enabled}")
   print(f"元素位置: {element.rect.location}")
   print(f"元素大小: {element.rect.size}")
   ```

3. **页面信息调试**
   ```python
   print(f"页面标题: {page.title}")
   print(f"当前URL: {page.url}")
   print(f"页面状态: {page.ready_state}")
   ```

### 📊 性能监控

1. **执行时间监控**
   ```python
   import time
   start_time = time.time()

   # 执行操作
   page.get('https://example.com')
   elements = page.eles('.item')

   end_time = time.time()
   print(f"执行时间: {end_time - start_time:.2f}秒")
   ```

2. **内存使用监控**
   ```python
   import psutil
   import os

   process = psutil.Process(os.getpid())
   print(f"内存使用: {process.memory_info().rss / 1024 / 1024:.2f} MB")
   ```

---

## 🔄 版本更新与迁移

### 📋 版本历史重要变更

#### v4.0+ 主要特性
- 内置动作链 (Action Chains)
- 优化的页面加载控制
- 增强的数据包捕获功能
- 新的标签页创建选项

#### v3.2+ 主要变更
- 新的超时设置语法
- 改进的配置管理
- 增强的错误处理

### 🔄 迁移指南

从旧版本迁移时需要注意：

1. **API变更**
   ```python
   # 旧版本 (v3.1及以下)
   page.set_timeouts(20, 30, 40)

   # 新版本 (v3.2+)
   page.set.timeouts(20, 30, 40)
   ```

2. **配置文件更新**
   - 检查 `configs.ini` 文件格式
   - 更新浏览器路径配置
   - 验证新增的配置选项

---

*本文档基于 DrissionPage v4.1.0.13 生成，使用 Context7、DeepWiki 和 MCP-Git-Ingest 工具进行数据收集和分析。*
00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000